#!/usr/bin/env python3
"""
Test script to verify MCP schema updates are working correctly.
This script tests the new MCP creation flow with the updated fields.
"""

import as<PERSON><PERSON>
import j<PERSON>
from typing import Dict, Any

# Test data for MCP creation with new fields
test_mcp_data = {
    "name": "Test MCP with New Fields",
    "description": "Testing the updated MCP schema with repo_name, git_user_name, and integrations",
    "git_url": "https://github.com/test/test-mcp.git",
    "git_branch": "main",
    "category": "general",
    "visibility": "private",
    "status": "active",
    "mcp_type": "stdio",
    "component_category": "database",
    # New fields
    "repo_name": "test-mcp",
    "git_user_name": "testuser",
    "integrations": ["postgresql", "redis", "mongodb"],
    # Old fields that should be ignored/removed
    # "env_keys": [],  # This should not be present
    # "oauth_details": None,  # This should not be present
}

def test_schema_validation():
    """Test that the new MCP schema validates correctly."""
    try:
        from app.schemas.mcp import MCPCreate
        
        # Test creating MCP with new fields
        mcp_create = MCPCreate(**test_mcp_data)
        print("✅ MCPCreate schema validation passed")
        print(f"   - repo_name: {mcp_create.repo_name}")
        print(f"   - git_user_name: {mcp_create.git_user_name}")
        print(f"   - integrations: {mcp_create.integrations}")
        
        # Test that old fields are not present
        assert not hasattr(mcp_create, 'env_keys'), "env_keys field should be removed"
        assert not hasattr(mcp_create, 'oauth_details'), "oauth_details field should be removed"
        print("✅ Old fields (env_keys, oauth_details) successfully removed")
        
        return True
    except Exception as e:
        print(f"❌ Schema validation failed: {e}")
        return False

def test_marketplace_schema():
    """Test that marketplace schema has been updated."""
    try:
        from app.schemas.marketplace import MarketplaceMCPDetail
        
        marketplace_data = {
            "id": "test-id",
            "name": "Test Marketplace MCP",
            "description": "Test description",
            "logo": "test-logo.png",
            "mcp_tools_config": {},
            "visibility": "public",
            "owner_id": "test-owner",
            "owner_type": "user",
            "user_ids": [],
            "category": "general",
            "tags": "test,mcp",
            "status": "active",
            "config": [],
            "git_url": "https://github.com/test/test.git",
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z",
            "average_rating": 4.5,
            "api_documentation": "Test docs",
            "capabilities": ["test"],
            "is_added": False,
            "component_category": "database",
            # New fields
            "repo_name": "test-marketplace-mcp",
            "git_user_name": "marketplaceuser",
            "integrations": ["api", "webhook"],
            "url": "https://api.test.com"
        }
        
        marketplace_mcp = MarketplaceMCPDetail(**marketplace_data)
        print("✅ MarketplaceMCPDetail schema validation passed")
        print(f"   - repo_name: {marketplace_mcp.repo_name}")
        print(f"   - git_user_name: {marketplace_mcp.git_user_name}")
        print(f"   - integrations: {marketplace_mcp.integrations}")
        print(f"   - url: {marketplace_mcp.url}")
        
        return True
    except Exception as e:
        print(f"❌ Marketplace schema validation failed: {e}")
        return False

def test_removed_schemas():
    """Test that removed schemas are no longer importable."""
    try:
        from app.schemas.mcp import EnvKey
        print("❌ EnvKey schema should have been removed")
        return False
    except ImportError:
        print("✅ EnvKey schema successfully removed")
    
    try:
        from app.schemas.mcp import OAuthDetails
        print("❌ OAuthDetails schema should have been removed")
        return False
    except ImportError:
        print("✅ OAuthDetails schema successfully removed")
    
    try:
        from app.schemas.mcp import EnvCredentialStatus
        print("❌ EnvCredentialStatus schema should have been removed")
        return False
    except ImportError:
        print("✅ EnvCredentialStatus schema successfully removed")
    
    return True

def main():
    """Run all tests."""
    print("🧪 Testing MCP Schema Updates")
    print("=" * 50)
    
    tests = [
        ("Schema Validation", test_schema_validation),
        ("Marketplace Schema", test_marketplace_schema),
        ("Removed Schemas", test_removed_schemas),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name}...")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} failed")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! MCP schema updates are working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
