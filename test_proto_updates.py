#!/usr/bin/env python3
"""
Test script to verify that the proto file has been updated correctly
and all new fields are working as expected.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_proto_fields():
    """Test that the proto file has the new fields and removed old ones."""
    try:
        from app.grpc_ import mcp_pb2
        
        print("🧪 Testing Proto File Updates")
        print("=" * 50)
        
        # Test CreateMCPRequest has new fields
        print("\n📋 Testing CreateMCPRequest...")
        request = mcp_pb2.CreateMCPRequest()
        
        # Test new fields exist
        assert hasattr(request, 'repo_name'), "repo_name field missing from CreateMCPRequest"
        assert hasattr(request, 'git_user_name'), "git_user_name field missing from CreateMCPRequest"
        assert hasattr(request, 'integrations'), "integrations field missing from CreateMCPRequest"
        
        # Test we can set new fields
        request.repo_name = "test-repo"
        request.git_user_name = "testuser"
        request.integrations.extend(["postgresql", "redis"])
        
        print("✅ CreateMCPRequest has all new fields")
        print(f"   - repo_name: {request.repo_name}")
        print(f"   - git_user_name: {request.git_user_name}")
        print(f"   - integrations: {list(request.integrations)}")
        
        # Test UpdateMCPRequest has new fields
        print("\n📋 Testing UpdateMCPRequest...")
        update_request = mcp_pb2.UpdateMCPRequest()
        
        assert hasattr(update_request, 'repo_name'), "repo_name field missing from UpdateMCPRequest"
        assert hasattr(update_request, 'git_user_name'), "git_user_name field missing from UpdateMCPRequest"
        assert hasattr(update_request, 'integrations'), "integrations field missing from UpdateMCPRequest"
        
        update_request.repo_name = "updated-repo"
        update_request.git_user_name = "updateduser"
        update_request.integrations.extend(["mongodb", "elasticsearch"])
        
        print("✅ UpdateMCPRequest has all new fields")
        print(f"   - repo_name: {update_request.repo_name}")
        print(f"   - git_user_name: {update_request.git_user_name}")
        print(f"   - integrations: {list(update_request.integrations)}")
        
        # Test MCP message has new fields
        print("\n📋 Testing MCP message...")
        mcp = mcp_pb2.MCP()
        
        assert hasattr(mcp, 'repo_name'), "repo_name field missing from MCP"
        assert hasattr(mcp, 'git_user_name'), "git_user_name field missing from MCP"
        assert hasattr(mcp, 'integrations'), "integrations field missing from MCP"
        assert hasattr(mcp, 'url'), "url field missing from MCP"
        
        mcp.repo_name = "mcp-repo"
        mcp.git_user_name = "mcpuser"
        mcp.integrations.extend(["api", "webhook", "database"])
        mcp.url = "https://api.example.com"
        
        print("✅ MCP message has all new fields")
        print(f"   - repo_name: {mcp.repo_name}")
        print(f"   - git_user_name: {mcp.git_user_name}")
        print(f"   - integrations: {list(mcp.integrations)}")
        print(f"   - url: {mcp.url}")
        
        # Test MarketplaceMCP has new fields
        print("\n📋 Testing MarketplaceMCP message...")
        marketplace_mcp = mcp_pb2.MarketplaceMCP()
        
        assert hasattr(marketplace_mcp, 'repo_name'), "repo_name field missing from MarketplaceMCP"
        assert hasattr(marketplace_mcp, 'git_user_name'), "git_user_name field missing from MarketplaceMCP"
        assert hasattr(marketplace_mcp, 'integrations'), "integrations field missing from MarketplaceMCP"
        assert hasattr(marketplace_mcp, 'url'), "url field missing from MarketplaceMCP"
        
        marketplace_mcp.repo_name = "marketplace-repo"
        marketplace_mcp.git_user_name = "marketplaceuser"
        marketplace_mcp.integrations.extend(["social", "analytics"])
        marketplace_mcp.url = "https://marketplace.example.com"
        
        print("✅ MarketplaceMCP message has all new fields")
        print(f"   - repo_name: {marketplace_mcp.repo_name}")
        print(f"   - git_user_name: {marketplace_mcp.git_user_name}")
        print(f"   - integrations: {list(marketplace_mcp.integrations)}")
        print(f"   - url: {marketplace_mcp.url}")
        
        return True
        
    except Exception as e:
        print(f"❌ Proto field test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Testing MCP Proto Updates")
    print("=" * 50)
    
    tests = [
        ("Proto Fields", test_proto_fields),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name} Test...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} test passed")
            else:
                print(f"❌ {test_name} test failed")
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        print("✅ Proto file has been successfully updated with new fields")
        print("✅ New fields are working correctly")
        return True
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
